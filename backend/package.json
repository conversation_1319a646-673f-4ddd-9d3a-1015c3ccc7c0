{"name": "ai-article-summarizer", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node ./dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "lint": "eslint src --ignore-path .eslint<PERSON>ore --ext .ts", "lint:fix": "npx eslint src --fix", "prettier": "prettier --ignore-path .gitignore --write \"./src/**/*.+(js|ts|json)\"", "prettier:fix": "npx prettier --write src", "postinstall": "prisma generate", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "6.12.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "http-status": "^2.1.0", "jsonwebtoken": "^9.0.2", "openai": "^5.10.2", "zod": "^4.0.5"}, "devDependencies": {"@types/bcrypt": "^6.0.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "prettier": "^3.6.2", "prisma": "^6.12.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}