{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"no-unused-vars": "warn", "no-unused-expressions": "error", "prefer-const": "error", "no-console": "warn", "no-undef": "error"}, "globals": {"process": "readonly"}}