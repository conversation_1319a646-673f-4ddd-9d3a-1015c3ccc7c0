"use client"

import { useMutation, useQueryClient } from "@tanstack/react-query"

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/hooks/use-toast"
import { deleteArticle } from "@/lib/api"

interface DeleteConfirmDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    articleId: string
    articleTitle: string
}

export function DeleteConfirmDialog({ open, onOpenChange, articleId, articleTitle }: DeleteConfirmDialogProps) {
    const { toast } = useToast()
    const queryClient = useQueryClient()

    const deleteMutation = useMutation({
        mutationFn: deleteArticle,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["articles"] })
            toast({
                title: "Article deleted",
                description: "The article has been deleted successfully.",
            })
            onOpenChange(false)
        },
        onError: () => {
            toast({
                title: "Error",
                description: "Failed to delete article. Please try again.",
                variant: "destructive",
            })
        },
    })

    const handleDelete = () => {
        deleteMutation.mutate(articleId)
    }

    return (
        <AlertDialog open={open} onOpenChange={onOpenChange}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete the article{" "}
                        <span className="font-medium">&quot;{articleTitle}&quot;</span> and remove it from our servers.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                        onClick={handleDelete}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                        {deleteMutation.isPending ? "Deleting..." : "Delete"}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}
